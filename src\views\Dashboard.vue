<template>
  <div class="dashboard">
    <h2 class="page-title">信息中心</h2>
    
    <!-- 统计卡片 -->
    <el-row :gutter="24" class="stats-row">
      <el-col :xs="12" :sm="6" v-for="stat in stats" :key="stat.title">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon" :style="{ backgroundColor: stat.color }">
              <el-icon size="24">
                <component :is="stat.icon" />
              </el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stat.value }}</div>
              <div class="stat-title">{{ stat.title }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 用户数据管理 -->
    <el-row class="charts-row">
      <el-col :span="24">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>用户数据管理</span>
              <div class="header-actions">
                <el-input
                  v-model="userSearch"
                  placeholder="搜索用户昵称"
                  style="width: 200px; margin-right: 10px;"
                  @keyup.enter="handleUserSearch"
                >
                  <template #append>
                    <el-button @click="handleUserSearch">
                      <el-icon><Search /></el-icon>
                    </el-button>
                  </template>
                </el-input>
                <el-button type="primary" @click="loadUsers">
                  <el-icon><Refresh /></el-icon>
                  刷新
                </el-button>
              </div>
            </div>
          </template>
          
          <div class="table-container">
            <el-table
              :data="users"
              :loading="userLoading"
              style="width: 100%;"
              :height="tableHeight"
              border
              stripe
            >
              <el-table-column 
                prop="_openid" 
                :width="isUserIdCollapsed ? 120 : 380" 
                show-overflow-tooltip
              >
                <template #header>
                  <div 
                    class="user-id-header" 
                    @click="toggleUserIdColumn"
                  >
                    <span>用户ID</span>
                    <el-icon class="collapse-icon" :class="{ 'expanded': !isUserIdCollapsed }">
                      <ArrowRight />
                    </el-icon>
                  </div>
                </template>
                <template #default="{ row }">
                  <span v-if="isUserIdCollapsed" class="user-id-short">
                    {{ row._openid ? row._openid.slice(0, 8) + '...' : '' }}
                  </span>
                  <span v-else class="user-id-full">
                    {{ row._openid }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column prop="nickName" label="昵称" width="220" />
              <el-table-column prop="phoneNumber" label="手机号" width="130" />
              <el-table-column prop="reward" label="积分" width="80" />
              <el-table-column prop="supplyCount" label="供应数量" width="100" />
              <el-table-column label="编辑供应" width="120" align="center">
                <template #default="{ row }">
                  <el-button
                    v-if="row.supplyCount && row.supplyCount > 0"
                    type="primary"
                    size="small"
                    @click="handleEditSupply(row)"
                    plain
                    class="supply-edit-btn"
                  >
                    <el-icon><Setting /></el-icon>
                    编辑供应
                  </el-button>
                  <el-tag v-else type="info" size="small" class="no-supply-tag">
                    暂无供应
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="demandCount" label="求购数量" width="100" />
              <el-table-column label="位置" width="120">
                <template #default="{ row }">
                  <el-button 
                    link 
                    type="primary" 
                    size="small" 
                    @click="handleParseLocation(row)"
                    :loading="locationParsingMap[row._id]"
                    :disabled="!row.nowPos"
                  >
                    <el-icon><Location /></el-icon>
                    <span v-if="!row.nowPos" class="text-gray">无位置信息</span>
                    <span v-else-if="locationParsingMap[row._id]">解析中...</span>
                    <span v-else>点击解析位置</span>
                  </el-button>
                </template>
              </el-table-column>
              <el-table-column prop="province" label="省份" width="100" />
              <el-table-column label="最后登录" width="160">
                <template #default="{ row }">
                  <span v-if="row.lastLoginTime">{{ formatDate(row.lastLoginTime) }}</span>
                  <span v-else class="text-gray">从未登录</span>
                </template>
              </el-table-column>
              <el-table-column label="创建时间" width="160">
                <template #default="{ row }">
                  {{ formatDate(row.createTime) }}
                </template>
              </el-table-column>
              <el-table-column label="操作" width="140" fixed="right">
                <template #default="{ row }">
                  <el-button link type="primary" size="small" @click="handleViewUser(row)">
                    <el-icon><View /></el-icon>
                    查看
                  </el-button>
                  <el-button link type="warning" size="small" @click="handleEditUser(row)">
                    <el-icon><Edit /></el-icon>
                    编辑
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            
            <div class="pagination-wrapper">
              <div class="custom-pagination-container">
                <div class="custom-total">
                  <span class="info-text">本页 {{ users.length }} 个数据</span>
                </div>
              <el-pagination
                v-model:current-page="userPagination.page"
                :total="userPagination.total"
                :page-size="80"
                :small="false"
                :background="true"
                  layout="prev, pager, next, jumper"
                @current-change="handleUserPageChange"
              />
              </div>
              <div class="pagination-info">
                <span class="info-text">
                  当前第 {{ userPagination.page }} / {{ Math.ceil(userPagination.total / userPagination.limit) || 1 }} 页
                </span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 用户详情查看弹窗 -->
    <el-dialog
      v-model="viewDialogVisible"
      title="用户详情"
      width="600px"
      :before-close="handleCloseViewDialog"
    >
      <div v-if="currentUser" class="user-detail">
        <el-descriptions :column="2" border :label-style="{ width: '120px' }">
          <el-descriptions-item label="用户ID">{{ currentUser._openid }}</el-descriptions-item>
          <el-descriptions-item label="昵称">{{ currentUser.nickName }}</el-descriptions-item>
          <el-descriptions-item label="电话号">{{ currentUser.phoneNumber || '未绑定' }}</el-descriptions-item>
          <el-descriptions-item label="积分">{{ currentUser.reward || 0 }}</el-descriptions-item>
          <el-descriptions-item label="供应数量">{{ currentUser.supplyCount || 0 }}</el-descriptions-item>
          <el-descriptions-item label="求购数量">{{ currentUser.demandCount || 0 }}</el-descriptions-item>
          <el-descriptions-item label="省份">{{ currentUser.province || '未知' }}</el-descriptions-item>
          <el-descriptions-item label="最后登录">
            {{ currentUser.lastLoginTime ? formatDate(currentUser.lastLoginTime) : '从未登录' }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ formatDate(currentUser.createTime) }}
          </el-descriptions-item>
          <el-descriptions-item label="更新时间">
            {{ currentUser.updateTime ? formatDate(currentUser.updateTime) : '无' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <template #footer>
        <el-button @click="viewDialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="handleEditFromView">编辑用户</el-button>
      </template>
    </el-dialog>

    <!-- 用户编辑弹窗 -->
    <el-dialog
      v-model="editDialogVisible"
      title="编辑用户"
      width="600px"
      :before-close="handleCloseEditDialog"
    >
      <el-form
        ref="editFormRef"
        :model="editForm"
        :rules="editRules"
        label-width="100px"
      >
        <el-form-item label="昵称" prop="nickName">
          <el-input v-model="editForm.nickName" placeholder="请输入用户昵称" />
        </el-form-item>
        <el-form-item label="积分" prop="reward">
          <el-input-number v-model="editForm.reward" :min="0" :max="999999" />
        </el-form-item>
        <el-form-item label="省份" prop="province">
          <el-input v-model="editForm.province" placeholder="请输入省份" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="editDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSaveUser" :loading="saveLoading">保存</el-button>
      </template>
    </el-dialog>

    <!-- 位置信息弹窗 -->
    <el-dialog
      v-model="locationDialogVisible"
      title="位置信息详情"
      width="600px"
      :before-close="handleCloseLocationDialog"
    >
      <div v-if="locationInfo" class="location-detail">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="用户">{{ locationInfo.user.nickName }}</el-descriptions-item>
          <el-descriptions-item label="坐标">
            纬度: {{ locationInfo.coordinates.latitude.toFixed(6) }}，
            经度: {{ locationInfo.coordinates.longitude.toFixed(6) }}
          </el-descriptions-item>
          <el-descriptions-item label="详细地址">{{ locationInfo.address }}</el-descriptions-item>
          
          <el-descriptions-item label="地址组件" v-if="locationInfo.address_component">
            <div class="address-components">
              <el-tag v-if="locationInfo.address_component.province" type="info" size="small">
                {{ locationInfo.address_component.province }}
              </el-tag>
              <el-tag v-if="locationInfo.address_component.city" type="success" size="small">
                {{ locationInfo.address_component.city }}
              </el-tag>
              <el-tag v-if="locationInfo.address_component.district" type="warning" size="small">
                {{ locationInfo.address_component.district }}
              </el-tag>
              <el-tag v-if="locationInfo.address_component.street" size="small">
                {{ locationInfo.address_component.street }}
              </el-tag>
            </div>
          </el-descriptions-item>
          
          <el-descriptions-item label="附近POI" v-if="locationInfo.pois && locationInfo.pois.length > 0">
            <div class="poi-list">
              <div v-for="poi in locationInfo.pois.slice(0, 5)" :key="poi.id" class="poi-item">
                <el-icon><Location /></el-icon>
                <span>{{ poi.title }}</span>
                <small class="poi-distance">{{ poi.distance }}m</small>
              </div>
            </div>
          </el-descriptions-item>
        </el-descriptions>
      </div>
      
      <template #footer>
        <el-button type="primary" @click="handleCloseLocationDialog">关闭</el-button>
      </template>
    </el-dialog>

    <!-- 供应信息编辑弹窗 -->
    <el-dialog
      v-model="supplyDialogVisible"
      title="编辑供应信息"
      width="1200px"
      :before-close="handleCloseSupplyDialog"
      class="supply-dialog"
      :modal="true"
      :lock-scroll="true"
      :close-on-click-modal="false"
    >
      <div v-if="currentUserSupplies">
        <div class="supply-header">
          <div class="supply-header-content">
            <div class="user-info">
              <el-avatar :size="40" class="user-avatar" :src="currentSupplyUser?.avatarUrl">
                <template #default>
                  {{ currentSupplyUser?.nickName?.charAt(0) || 'U' }}
                </template>
              </el-avatar>
              <div class="user-details">
                <h3 class="user-name">{{ currentSupplyUser?.nickName }}</h3>
                <span class="supply-count">共 {{ supplies.length }} 条供应信息</span>
              </div>
            </div>
            <div class="header-actions">
              <el-button type="primary" @click="refreshSupplies" :loading="supplyLoading" size="default">
                <el-icon><Refresh /></el-icon>
                刷新数据
              </el-button>
            </div>
          </div>
        </div>

        <!-- 供应信息列表 -->
        <div v-if="supplies.length > 0" class="supply-list">
          <div v-for="supply in supplies" :key="supply._id" class="supply-item">
            <el-card class="supply-card" shadow="hover">
              <template #header>
                <div class="supply-card-header">
                  <div class="supply-title-section">
                    <el-tag type="success" size="small" class="supply-status-tag">
                      <el-icon><Document /></el-icon>
                      供应中
                    </el-tag>
                    <div class="supply-title-container">
                      <!-- 正常显示模式 -->
                      <h4 
                        v-if="!isEditingTitle[supply._id]" 
                        class="supply-title"
                        @dblclick="startEditTitle(supply)"
                      >
                        {{ supply.title || '未命名植物' }}
                      </h4>
                      <!-- 编辑模式 -->
                      <div v-else class="supply-title-edit">
                        <el-input
                          :ref="setTitleInputRef"
                          v-model="editingTitleValue"
                          size="small"
                          placeholder="请输入植物名称"
                          @blur="confirmEditTitle(supply)"
                          @keyup.enter="confirmEditTitle(supply)"
                          @keyup.escape="cancelEditTitle(supply)"
                        />
                      </div>
                      <!-- 编辑按钮 -->
                      <el-button
                        v-if="!isEditingTitle[supply._id]"
                        size="small"
                        type="text"
                        class="title-edit-btn"
                        @click="startEditTitle(supply)"
                        title="点击编辑标题"
                      >
                        <el-icon><Edit /></el-icon>
                      </el-button>
                    </div>
                  </div>
                  <div class="supply-actions">
                    <el-button size="small" type="primary" @click="handleEditSupplyItem(supply)" plain>
                      <el-icon><Edit /></el-icon>
                      编辑
                    </el-button>
                    <el-button size="small" type="danger" @click="handleDeleteSupplyItem(supply)" plain>
                      <el-icon><Delete /></el-icon>
                      删除
                    </el-button>
                  </div>
                </div>
              </template>
              
              <div class="supply-content">
                <el-row :gutter="16">
                  <!-- 基本信息区域 -->
                  <el-col :span="14">
                    <div class="supply-info-section">
                      <!-- 价格和数量信息 -->
                      <div class="price-quantity-row">
                        <div class="price-info">
                          <el-tag type="warning" size="large" class="price-tag">
                            <el-icon><Wallet /></el-icon>
                            ¥{{ supply.price || 0 }} / {{ supply.price_unit || '株' }}
                          </el-tag>
                        </div>
                        <div class="quantity-info">
                          <el-tag type="info" size="default">
                            库存: {{ supply.quantity || '0' }}
                          </el-tag>
                          <el-tag type="success" size="default" v-if="supply.quality">
                            {{ supply.quality }}
                          </el-tag>
                        </div>
                      </div>

                      <!-- 描述信息 -->
                      <div class="description-section" v-if="supply.content">
                        <p class="supply-description">{{ supply.content }}</p>
                      </div>

                      <!-- 联系信息 -->
                      <div class="contact-section">
                        <div class="contact-item" v-if="supply.phoneNumber">
                          <el-icon class="contact-icon"><Phone /></el-icon>
                          <span class="contact-text">{{ supply.phoneNumber }}</span>
                        </div>
                        <div class="contact-item" v-if="supply.address">
                          <el-icon class="contact-icon"><Location /></el-icon>
                          <span class="contact-text">{{ supply.address }}</span>
                        </div>
                      </div>
                    </div>
                  </el-col>

                  <!-- 规格参数区域 -->
                  <el-col :span="10">
                    <div class="supply-specs">
                      <div class="specs-header">
                        <el-icon class="specs-icon"><Tools /></el-icon>
                        <span class="specs-title">规格参数</span>
                      </div>
                      <div class="specs-grid">
                        <div class="spec-item" v-if="supply.height">
                          <span class="spec-label">高度</span>
                          <span class="spec-value">{{ supply.height }}cm</span>
                        </div>
                        <div class="spec-item" v-if="supply.meter_diameter">
                          <span class="spec-label">米径</span>
                          <span class="spec-value">{{ supply.meter_diameter }}cm</span>
                        </div>
                        <div class="spec-item" v-if="supply.thorax_diameter">
                          <span class="spec-label">胸径</span>
                          <span class="spec-value">{{ supply.thorax_diameter }}cm</span>
                        </div>
                        <div class="spec-item" v-if="supply.ground_diameter">
                          <span class="spec-label">地径</span>
                          <span class="spec-value">{{ supply.ground_diameter }}cm</span>
                        </div>
                        <div class="spec-item" v-if="supply.canopy">
                          <span class="spec-label">冠幅</span>
                          <span class="spec-value">{{ supply.canopy }}cm</span>
                        </div>
                        <div class="spec-item" v-if="supply.cup">
                          <span class="spec-label">杯口</span>
                          <span class="spec-value">{{ supply.cup }}cm</span>
                        </div>
                        <div class="spec-item" v-if="supply.branchPos">
                          <span class="spec-label">分枝点</span>
                          <span class="spec-value">{{ supply.branchPos }}cm</span>
                        </div>
                        <div class="spec-item" v-if="supply.clumpCount">
                          <span class="spec-label">丛生数量</span>
                          <span class="spec-value">{{ supply.clumpCount }}</span>
                        </div>
                        <div class="spec-item" v-if="supply.clumpDiameter">
                          <span class="spec-label">杆径</span>
                          <span class="spec-value">{{ supply.clumpDiameter }}cm</span>
                        </div>
                      </div>
                    </div>
                  </el-col>
                </el-row>
                
                <!-- 图片预览 -->
                <div v-if="getSupplyImages(supply).length > 0" class="supply-images">
                  <div class="images-header">
                    <el-icon class="images-icon"><Picture /></el-icon>
                    <span class="images-title">植物图片</span>
                    <el-tag size="small" type="info">{{ getSupplyImages(supply).length }} 张</el-tag>
                  </div>
                  <div class="image-grid">
                    <div
                      v-for="(image, imgIndex) in getSupplyImages(supply)"
                      :key="imgIndex"
                      class="image-item"
                      @click="previewImage(image.url)"
                    >
                      <el-image
                        :src="image.url"
                        :preview-src-list="getSupplyImages(supply).map(img => img.url)"
                        fit="cover"
                        class="supply-image"
                        :hide-on-click-modal="true"
                      >
                        <template #error>
                          <div class="image-error">
                            <el-icon><Picture /></el-icon>
                            <span>加载失败</span>
                          </div>
                        </template>
                      </el-image>
                      <div v-if="image.captureTime" class="image-time">
                        {{ formatImageTime(image.captureTime) }}
                      </div>
                      <div class="image-overlay" @click.stop="openImagePreview(image.url)">
                        <el-icon class="preview-icon"><View /></el-icon>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </el-card>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-else-if="!supplyLoading" class="empty-supply">
          <el-empty description="该用户暂无供应信息">
            <el-button type="primary">引导用户发布供应信息</el-button>
          </el-empty>
        </div>

        <!-- 加载状态 -->
        <div v-if="supplyLoading" class="loading-container">
          <el-skeleton :rows="3" animated />
        </div>
      </div>
      
      <template #footer>
        <el-button @click="handleCloseSupplyDialog">关闭</el-button>
      </template>
    </el-dialog>

    <!-- 供应信息编辑表单弹窗 -->
    <el-dialog
      v-model="supplyEditDialogVisible"
      title="编辑供应详情"
      width="1000px"
      :before-close="handleCloseSupplyEditDialog"
      class="supply-edit-dialog"
      :modal="true"
      :lock-scroll="true"
      :close-on-click-modal="false"
      destroy-on-close
    >
      <el-form
        ref="supplyEditFormRef"
        :model="supplyEditForm"
        :rules="supplyEditRules"
        label-width="100px"
        label-position="left"
        class="supply-edit-form"
      >
        <el-tabs v-model="activeEditTab" type="card" class="supply-edit-tabs">
          <!-- 基本信息 -->
          <el-tab-pane label="基本信息" name="basic">
            <div class="basic-info-container">
              <!-- 基础信息区域 -->
              <el-card class="info-section" shadow="never">
                <template #header>
                  <div class="section-header">
                    <el-icon class="section-icon"><Document /></el-icon>
                    <span class="section-title">基础信息</span>
                  </div>
                </template>
                <el-row :gutter="24">
                  <el-col :span="12">
                    <el-form-item label="植物名称" prop="title">
                      <el-input
                        v-model="supplyEditForm.title"
                        placeholder="请输入植物名称"
                        size="default"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="产品质量" prop="quality">
                      <el-input
                        v-model="supplyEditForm.quality"
                        placeholder="质量等级描述"
                        size="default"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="简介描述" prop="content">
                      <el-input
                        v-model="supplyEditForm.content"
                        type="textarea"
                        :rows="3"
                        placeholder="请输入植物详细描述"
                        resize="none"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-card>

              <!-- 价格信息区域 -->
              <el-card class="info-section" shadow="never">
                <template #header>
                  <div class="section-header">
                    <el-icon class="section-icon"><Wallet /></el-icon>
                    <span class="section-title">价格信息</span>
                  </div>
                </template>
                <el-row :gutter="24">
                  <el-col :span="8">
                    <el-form-item label="上车价" prop="price">
                      <el-input-number
                        v-model="supplyEditForm.price"
                        :min="0"
                        :precision="2"
                        placeholder="价格"
                        size="default"
                        style="width: 100%"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="价格单位" prop="price_unit">
                      <el-select
                        v-model="supplyEditForm.price_unit"
                        placeholder="选择单位"
                        size="default"
                        style="width: 100%"
                      >
                        <el-option label="株" value="株" />
                        <el-option label="棵" value="棵" />
                        <el-option label="平方米" value="平方米" />
                        <el-option label="米" value="米" />
                        <el-option label="盆" value="盆" />
                        <el-option label="个" value="个" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="供应数量" prop="quantity">
                      <el-input
                        v-model="supplyEditForm.quantity"
                        placeholder="可供应数量"
                        size="default"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-card>

              <!-- 规格参数区域 -->
              <el-card class="info-section" shadow="never">
                <template #header>
                  <div class="section-header">
                    <el-icon class="section-icon"><Tools /></el-icon>
                    <span class="section-title">规格参数</span>
                  </div>
                </template>
                <el-row :gutter="24">
                  <el-col :span="8">
                    <el-form-item label="高度(cm)" prop="height">
                      <el-input-number
                        v-model="supplyEditForm.height"
                        :min="0"
                        size="default"
                        style="width: 100%"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="米径(cm)" prop="meter_diameter">
                      <el-input-number
                        v-model="supplyEditForm.meter_diameter"
                        :min="0"
                        size="default"
                        style="width: 100%"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="胸径(cm)" prop="thorax_diameter">
                      <el-input-number
                        v-model="supplyEditForm.thorax_diameter"
                        :min="0"
                        size="default"
                        style="width: 100%"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="地径(cm)" prop="ground_diameter">
                      <el-input-number
                        v-model="supplyEditForm.ground_diameter"
                        :min="0"
                        size="default"
                        style="width: 100%"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="冠幅(cm)" prop="canopy">
                      <el-input-number
                        v-model="supplyEditForm.canopy"
                        :min="0"
                        size="default"
                        style="width: 100%"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="杯口(cm)" prop="cup">
                      <el-input-number
                        v-model="supplyEditForm.cup"
                        :min="0"
                        size="default"
                        style="width: 100%"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="分枝点(cm)" prop="branchPos">
                      <el-input-number
                        v-model="supplyEditForm.branchPos"
                        :min="0"
                        size="default"
                        style="width: 100%"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="丛生数量" prop="clumpCount">
                      <el-input-number
                        v-model="supplyEditForm.clumpCount"
                        :min="0"
                        size="default"
                        style="width: 100%"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="杆径(cm)" prop="clumpDiameter">
                      <el-input-number
                        v-model="supplyEditForm.clumpDiameter"
                        :min="0"
                        size="default"
                        style="width: 100%"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-card>

              <!-- 联系信息区域 -->
              <el-card class="info-section" shadow="never">
                <template #header>
                  <div class="section-header">
                    <el-icon class="section-icon"><Phone /></el-icon>
                    <span class="section-title">联系信息</span>
                  </div>
                </template>
                <el-row :gutter="24">
                  <el-col :span="12">
                    <el-form-item label="联系电话" prop="phoneNumber">
                      <el-input
                        v-model="supplyEditForm.phoneNumber"
                        placeholder="请输入联系电话"
                        size="default"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="地址" prop="address">
                      <el-input
                        v-model="supplyEditForm.address"
                        placeholder="请输入植物所在地址"
                        size="default"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-card>
            </div>
          </el-tab-pane>

          <!-- 图片管理 -->
          <el-tab-pane label="图片管理" name="images">
            <div class="image-management-container">
              <el-card class="image-section" shadow="never">
                <template #header>
                  <div class="section-header">
                    <el-icon class="section-icon"><Picture /></el-icon>
                    <span class="section-title">图片管理</span>
                    <div class="image-stats">
                      <el-tag type="info" size="small">
                        共 {{ getTotalImageCount() }} 张图片
                      </el-tag>
                    </div>
                  </div>
                </template>

                <div class="image-management">
                  <div v-if="supplyEditForm.imageList && supplyEditForm.imageList.length > 0" class="image-list">
                    <div class="image-list-header">
                      <h4>旧格式图片</h4>
                      <el-tag size="small" type="warning">{{ supplyEditForm.imageList.length }} 张</el-tag>
                    </div>
                    <div class="image-grid">
                      <div v-for="(url, index) in supplyEditForm.imageList" :key="'old-' + index" class="image-item">
                        <el-image
                          :src="processImageUrl(url)"
                          fit="cover"
                          class="supply-image"
                          @error="handleImageError"
                          @click="openImagePreview(processImageUrl(url))"
                        >
                          <template #error>
                            <div class="image-error">
                              <el-icon><Picture /></el-icon>
                              <span>加载失败</span>
                            </div>
                          </template>
                          <template #placeholder>
                            <div class="image-loading">
                              <el-icon class="is-loading"><Loading /></el-icon>
                            </div>
                          </template>
                        </el-image>
                        <!-- 预览覆盖层 -->
                        <div class="image-overlay" @click.stop="openImagePreview(processImageUrl(url))">
                          <el-icon class="preview-icon"><View /></el-icon>
                        </div>
                        <div class="image-actions">
                          <el-button
                            size="small"
                            type="danger"
                            @click.stop="confirmRemoveOldImage(index)"
                            circle
                          >
                            <el-icon><Delete /></el-icon>
                          </el-button>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div v-if="supplyEditForm.newImageList && supplyEditForm.newImageList.length > 0" class="image-list">
                    <div class="image-list-header">
                      <h4>新格式图片</h4>
                      <el-tag size="small" type="success">{{ supplyEditForm.newImageList.length }} 张</el-tag>
                    </div>
                    <div class="image-grid">
                      <div v-for="(image, index) in supplyEditForm.newImageList" :key="'new-' + index" class="image-item">
                        <el-image
                          :src="processImageUrl(image.url)"
                          fit="cover"
                          class="supply-image"
                          @error="handleImageError"
                          @click="openImagePreview(processImageUrl(image.url))"
                        >
                          <template #error>
                            <div class="image-error">
                              <el-icon><Picture /></el-icon>
                              <span>加载失败</span>
                            </div>
                          </template>
                          <template #placeholder>
                            <div class="image-loading">
                              <el-icon class="is-loading"><Loading /></el-icon>
                            </div>
                          </template>
                        </el-image>
                        <div class="image-time">
                          {{ formatImageTime(image.captureTime) }}
                        </div>
                        <!-- 预览覆盖层 -->
                        <div class="image-overlay" @click.stop="openImagePreview(processImageUrl(image.url))">
                          <el-icon class="preview-icon"><View /></el-icon>
                        </div>
                        <div class="image-actions">
                          <el-button
                            size="small"
                            type="danger"
                            @click.stop="confirmRemoveNewImage(index)"
                            circle
                          >
                            <el-icon><Delete /></el-icon>
                          </el-button>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div v-if="(!supplyEditForm.imageList || supplyEditForm.imageList.length === 0) &&
                           (!supplyEditForm.newImageList || supplyEditForm.newImageList.length === 0)"
                       class="no-images">
                    <el-empty description="暂无图片">
                      <template #image>
                        <el-icon size="60" color="#c0c4cc"><Picture /></el-icon>
                      </template>
                    </el-empty>
                  </div>
                </div>
              </el-card>
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCloseSupplyEditDialog" size="default">
            <el-icon><Close /></el-icon>
            取消
          </el-button>
          <el-button type="primary" @click="handleSaveSupplyEdit" :loading="saveSupplyLoading" size="default">
            <el-icon><Check /></el-icon>
            保存修改
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 图片预览弹窗 -->
    <el-dialog
      v-model="imagePreviewVisible"
      :show-close="false"
      :close-on-click-modal="true"
      :close-on-press-escape="true"
      width="90%"
      top="5vh"
      class="image-preview-dialog"
      @close="closeImagePreview"
    >
      <div class="image-preview-container" @click="closeImagePreview">
        <div class="image-preview-content">
          <!-- 主要预览图片 -->
          <div class="main-image-container">
            <img 
              :src="currentPreviewImage" 
              :alt="'预览图片'" 
              class="preview-main-image"
              @click="closeImagePreview"
            />
          </div>
          

        </div>
        
        <!-- 关闭按钮 -->
        <div class="close-btn" @click="closeImagePreview">
          <el-icon size="24"><Close /></el-icon>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, nextTick } from 'vue'
import { getDashboardStats, getAllUsers, updateUser } from '../api/dashboard'
import { reverseGeocode } from '../api/geocoder'
import { getUserSupplies, updateSupply, deleteSupply } from '../api/supply'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search,
  Refresh,
  Location,
  View,
  Edit,
  Setting,
  Picture,
  Delete,
  Close,
  Check,
  Document,
  Wallet,
  Tools,
  Phone,
  ArrowRight,
  Loading
} from '@element-plus/icons-vue'

// 统计数据
const stats = ref([
  {
    title: '用户总量',
    value: '0',
    icon: 'User',
    color: '#409EFF',
    trend: 12.5
  },
  {
    title: '登录次数',
    value: '0',
    icon: 'Key',
    color: '#67C23A',
    trend: 8.3
  },
  {
    title: '发布信息',
    value: '0',
    icon: 'Document',
    color: '#E6A23C',
    trend: -2.1
  },
  {
    title: '浏览统计',
    value: '0',
    icon: 'View',
    color: '#F56C6C',
    trend: 15.8
  }
])

// 用户数据
const users = ref([])
const userLoading = ref(false)
const userPagination = ref({
  page: 1,
  limit: 80,
  total: 0,
  totalPages: 0
})
const userSearch = ref('')

// 用户ID列折叠状态
const isUserIdCollapsed = ref(true)

// 弹窗状态
const viewDialogVisible = ref(false)
const editDialogVisible = ref(false)
const currentUser = ref(null)
const saveLoading = ref(false)

// 位置解析相关状态
const locationParsingMap = ref({}) // 记录每个用户的解析状态
const locationDialogVisible = ref(false)
const locationInfo = ref(null)

// 供应信息相关状态
const supplyDialogVisible = ref(false)
const supplyEditDialogVisible = ref(false)
const currentSupplyUser = ref(null)
const currentUserSupplies = ref(null)
const supplies = ref([])
const supplyLoading = ref(false)
const saveSupplyLoading = ref(false)
const activeEditTab = ref('basic')

// 内联编辑标题相关状态
const isEditingTitle = ref({}) // 记录每个供应项是否在编辑标题
const editingTitleValue = ref('') // 编辑中的标题值
const titleInputRefs = ref({})

// 图片预览相关状态
const imagePreviewVisible = ref(false)
const currentPreviewImage = ref('')

// 供应信息编辑表单
const supplyEditFormRef = ref(null)
const supplyEditForm = ref({
  _id: '',
  title: '',
  content: '',
  price: 0,
  price_unit: '株',
  quantity: '',
  quality: '',
  phoneNumber: '',
  address: '',
  height: null,
  meter_diameter: null,
  thorax_diameter: null,
  ground_diameter: null,
  canopy: null,
  cup: null,
  branchPos: null,
  clumpCount: null,
  clumpDiameter: null,
  imageList: [],
  newImageList: []
})

// 供应信息表单验证规则
const supplyEditRules = {
  title: [
    { required: true, message: '请输入植物名称', trigger: 'blur' }
  ],
  price: [
    { required: true, message: '请输入价格', trigger: 'blur' },
    { type: 'number', min: 0, message: '价格不能为负数', trigger: 'blur' }
  ],
  price_unit: [
    { required: true, message: '请选择价格单位', trigger: 'change' }
  ]
}

// 编辑表单
const editFormRef = ref(null)
const editForm = ref({
  _id: '',
  nickName: '',
  reward: 0,
  province: ''
})

// 表单验证规则
const editRules = {
  nickName: [
    { required: true, message: '请输入用户昵称', trigger: 'blur' },
    { min: 2, max: 20, message: '昵称长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  reward: [
    { required: true, message: '请输入积分', trigger: 'blur' }
  ]
}

// 计算表格高度
const tableHeight = computed(() => {
  // 进一步增加表格高度，显示更多数据
  // 在小屏幕上使用更小的高度
  if (window.innerWidth <= 768) {
    return 'calc(100vh - 250px)'
  }
  return 'calc(100vh - 280px)'
})

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '无'
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 获取统计数据
const loadStats = async () => {
  try {
    const response = await getDashboardStats()
    

    
    // 处理响应数据 - 兼容新的响应格式
    const statsData = response.data || response
    
    if (statsData) {
      stats.value = [
        {
          title: '用户总量',
          value: statsData.totalUsers?.toLocaleString() || '0',
          icon: 'User',
          color: '#409EFF',
          trend: 12.5
        },
        {
          title: '登录次数',
          value: statsData.totalLogined?.toLocaleString() || '0',
          icon: 'Key',
          color: '#67C23A',
          trend: 8.3
        },
        {
          title: '发布信息',
          value: statsData.totalPost?.toLocaleString() || '0',
          icon: 'Document',
          color: '#E6A23C',
          trend: -2.1
        },
        {
          title: '浏览统计',
          value: statsData.totalView?.toLocaleString() || '0',
          icon: 'View',
          color: '#F56C6C',
          trend: 15.8
        }
      ]
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
    ElMessage.warning('统计数据获取失败，显示默认数据')
  }
}

// 获取用户数据
const loadUsers = async (retryCount = 0) => {
  try {
    userLoading.value = true
    
    // 显示重试提示
    if (retryCount > 0) {
      ElMessage.info(`正在重试加载用户数据... (第${retryCount + 1}次尝试)`)
    }
    
    const params = {
      page: userPagination.value.page,
      limit: userPagination.value.limit,
      search: userSearch.value
    }
    
    console.log(`开始加载用户数据 (第${retryCount + 1}次尝试):`, params)
    const response = await getAllUsers(params)
    

    
    // 处理响应数据 - 兼容新的响应格式
    const result = response.data || response
    
    if (result) {
      users.value = result.users || []
      const newTotal = result.pagination?.total || 0
      const newTotalPages = result.pagination?.totalPages || 0
      
      // 更新分页信息
      userPagination.value.total = newTotal
      userPagination.value.totalPages = newTotalPages
      
      // 检查当前页码是否有效，如果无效则重置到第一页
      if (userPagination.value.page > newTotalPages && newTotalPages > 0) {
        userPagination.value.page = 1
        // 重新加载数据但不递归调用
        const reloadParams = { ...params, page: 1 }
        const reloadResponse = await getAllUsers(reloadParams)
        const reloadResult = reloadResponse.data || reloadResponse
        if (reloadResult) {
          users.value = reloadResult.users || []
        }
      }
      
      console.log(`成功加载 ${users.value.length} 条用户数据，总计 ${newTotal} 条`)
    }
  } catch (error) {
    console.error('获取用户数据失败:', error)
    
    // 如果是网络错误且重试次数少于3次，则重试
    if (retryCount < 3 && (error.message.includes('timeout') || error.message.includes('ECONNRESET') || error.code === 'ECONNABORTED')) {
      const delay = (retryCount + 1) * 3000 // 递增延迟：3秒、6秒、9秒
      console.log(`第 ${retryCount + 1} 次重试，${delay/1000}秒后重试...`)
      setTimeout(() => {
        loadUsers(retryCount + 1)
      }, delay)
      return
    }
    
    // 显示友好的错误信息
    let errorMessage = '获取用户数据失败，请稍后重试'
    if (error.message.includes('timeout') || error.code === 'ECONNABORTED') {
      errorMessage = '请求超时，请检查网络连接或稍后重试'
    } else if (error.message.includes('ECONNRESET')) {
      errorMessage = '连接被重置，请稍后重试'
    }
    
    ElMessage.error(errorMessage)
  } finally {
    userLoading.value = false
  }
}

// 搜索用户
const handleUserSearch = () => {
  userPagination.value.page = 1
  loadUsers()
}

// 页码变化
const handleUserPageChange = (page) => {
  userPagination.value.page = page
  loadUsers()
}



// 查看用户详情
const handleViewUser = (user) => {
  currentUser.value = { ...user }
  viewDialogVisible.value = true
}

// 编辑用户
const handleEditUser = (user) => {
  editForm.value = {
    _id: user._id,
    nickName: user.nickName || '',
    reward: user.reward || 0,
    province: user.province || ''
  }
  editDialogVisible.value = true
}

// 从查看弹窗进入编辑
const handleEditFromView = () => {
  handleEditUser(currentUser.value)
  viewDialogVisible.value = false
}

// 切换用户ID列折叠状态
const toggleUserIdColumn = () => {
  isUserIdCollapsed.value = !isUserIdCollapsed.value
}

// 保存用户编辑
const handleSaveUser = async () => {
  if (!editFormRef.value) return
  
  try {
    await editFormRef.value.validate()
    saveLoading.value = true
    
    // 调用API更新用户
    await updateUser(editForm.value)
    
    // 更新本地数据
    const index = users.value.findIndex(u => u._id === editForm.value._id)
    if (index !== -1) {
      users.value[index] = { ...users.value[index], ...editForm.value }
    }
    
    editDialogVisible.value = false
    ElMessage.success('用户更新成功')
  } catch (error) {
    console.error('保存用户失败:', error)
    ElMessage.error('保存失败，请重试')
  } finally {
    saveLoading.value = false
  }
}

// 关闭弹窗
const handleCloseViewDialog = () => {
  viewDialogVisible.value = false
  currentUser.value = null
}

const handleCloseEditDialog = () => {
  editDialogVisible.value = false
  editFormRef.value?.resetFields()
}

// 位置解析功能
const handleParseLocation = async (user) => {
  if (!user.nowPos) {
    ElMessage.warning('该用户没有位置信息')
    return
  }

  // 设置解析状态
  locationParsingMap.value[user._id] = true
  
  try {
    // 直接将原始geopoint数据传递给后端处理
    const response = await reverseGeocode(user.nowPos)
    
    // 检查响应格式 - 后端使用Response.success()返回的格式
    if (!response || response.code !== 200 || !response.data) {
      throw new Error(response?.message || '地址解析失败')
    }
    
    // 设置位置信息并显示弹窗
    locationInfo.value = {
      user: user,
      coordinates: response.data.coordinates,
      address: response.data.address,
      address_component: response.data.address_component,
      pois: response.data.pois || []
    }
    
    locationDialogVisible.value = true
    ElMessage.success('位置解析成功')
    
  } catch (error) {
    console.error('位置解析失败:', error)
    ElMessage.error(`位置解析失败: ${error.message}`)
  } finally {
    // 清除解析状态
    locationParsingMap.value[user._id] = false
  }
}

// 关闭位置信息弹窗
const handleCloseLocationDialog = () => {
  locationDialogVisible.value = false
  locationInfo.value = null
}

// 供应信息相关函数
// 处理编辑供应按钮点击
const handleEditSupply = async (user) => {
  try {
    currentSupplyUser.value = user
    currentUserSupplies.value = user
    supplyDialogVisible.value = true
    
    // 加载用户的供应信息
    await loadUserSupplies(user._openid)
  } catch (error) {
    console.error('打开供应信息编辑失败:', error)
    ElMessage.error('加载供应信息失败')
  }
}

// 加载用户供应信息
const loadUserSupplies = async (openid) => {
  try {
    supplyLoading.value = true
    console.log(`开始加载用户 ${openid} 的供应信息...`)
    
    const response = await getUserSupplies(openid)
    const result = response.data || response
    
    if (result && result.supplies) {
      supplies.value = result.supplies
      console.log(`成功加载 ${supplies.value.length} 条供应信息`)
    } else {
      supplies.value = []
      console.log('用户暂无供应信息')
    }
  } catch (error) {
    console.error('加载用户供应信息失败:', error)
    ElMessage.error('加载供应信息失败')
    supplies.value = []
  } finally {
    supplyLoading.value = false
  }
}

// 刷新供应信息
const refreshSupplies = async () => {
  if (currentSupplyUser.value && currentSupplyUser.value._openid) {
    await loadUserSupplies(currentSupplyUser.value._openid)
  }
}

// 动态获取API基础URL
const getImageApiBaseUrl = () => {
  // 获取当前访问的主机名
  const hostname = window.location.hostname
  const protocol = window.location.protocol
  
  // 如果是IP地址（局域网访问），使用相同的IP访问后端
  if (/^\d+\.\d+\.\d+\.\d+$/.test(hostname)) {
    return `${protocol}//${hostname}:3000`
  }
  
  // 如果是localhost或其他域名，使用localhost
  return 'http://localhost:3000'
}

// 处理图片URL，确保格式正确
const processImageUrl = (url) => {
  if (!url) return ''

  // 如果已经是完整URL，直接返回
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return url
  }

  // 如果是data URL，直接返回
  if (url.startsWith('data:')) {
    return url
  }

  // 处理腾讯云存储路径 cloud://
  if (url.startsWith('cloud://')) {
    const baseUrl = getImageApiBaseUrl()
    return `${baseUrl}/api/cloud-image?path=${encodeURIComponent(url)}`
  }

  // 如果是相对路径，直接返回
  if (url.startsWith('./') || url.startsWith('../')) {
    return url
  }

  // 如果是云存储文件名（包含 imageNew_ 或时间戳）
  if (url.includes('imageNew_') || url.includes('_')) {
    const baseUrl = getImageApiBaseUrl()
    return `${baseUrl}/api/cloud-image?filename=${encodeURIComponent(url)}`
  }

  // 普通文件名，拼接uploads路径
  const baseUrl = getImageApiBaseUrl()
  return `${baseUrl}/uploads/${url}`
}

// 获取供应信息的图片列表
const getSupplyImages = (supply) => {
  const images = []

  // 处理旧格式图片
  if (supply.imageList && Array.isArray(supply.imageList)) {
    supply.imageList.forEach(url => {
      if (url) {
        const processedUrl = processImageUrl(url)
        if (processedUrl) {
          images.push({ url: processedUrl, captureTime: null })
        }
      }
    })
  }

  // 处理新格式图片
  if (supply.newImageList && Array.isArray(supply.newImageList)) {
    supply.newImageList.forEach(image => {
      if (image && image.url) {
        const processedUrl = processImageUrl(image.url)
        if (processedUrl) {
          images.push({
            url: processedUrl,
            captureTime: image.captureTime
          })
        }
      }
    })
  }

  return images
}

// 格式化图片时间
const formatImageTime = (captureTime) => {
  if (!captureTime) return ''
  const date = new Date(captureTime)
  return date.toLocaleString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 预览图片
const previewImage = (url) => {
  // Element Plus 的图片预览功能会自动处理
  console.log('预览图片:', url)
}

// 打开图片预览弹窗
const openImagePreview = (imageUrl) => {
  currentPreviewImage.value = imageUrl
  imagePreviewVisible.value = true
}

// 关闭图片预览弹窗
const closeImagePreview = () => {
  imagePreviewVisible.value = false
  currentPreviewImage.value = ''
}

// 处理图片加载错误
const handleImageError = (error) => {
  console.error('图片加载失败:', error)
  console.log('当前图片URL处理情况:')
  if (supplyEditForm.value.imageList) {
    supplyEditForm.value.imageList.forEach((url, index) => {
      console.log(`旧格式图片 ${index}:`, url, '→', processImageUrl(url))
    })
  }
  if (supplyEditForm.value.newImageList) {
    supplyEditForm.value.newImageList.forEach((image, index) => {
      console.log(`新格式图片 ${index}:`, image.url, '→', processImageUrl(image.url))
    })
  }
}



// 编辑供应项目
const handleEditSupplyItem = (supply) => {
  // 复制供应信息到编辑表单
  supplyEditForm.value = {
    _id: supply._id,
    title: supply.title || '',
    content: supply.content || '',
    price: supply.price || 0,
    price_unit: supply.price_unit || '株',
    quantity: supply.quantity || '',
    quality: supply.quality || '',
    phoneNumber: supply.phoneNumber || '',
    address: supply.address || '',
    height: supply.height || null,
    meter_diameter: supply.meter_diameter || null,
    thorax_diameter: supply.thorax_diameter || null,
    ground_diameter: supply.ground_diameter || null,
    canopy: supply.canopy || null,
    cup: supply.cup || null,
    branchPos: supply.branchPos || null,
    clumpCount: supply.clumpCount || null,
    clumpDiameter: supply.clumpDiameter || null,
    imageList: supply.imageList ? [...supply.imageList] : [],
    newImageList: supply.newImageList ? [...supply.newImageList] : []
  }
  
  activeEditTab.value = 'basic'
  supplyEditDialogVisible.value = true
}

// 删除供应项目
const handleDeleteSupplyItem = async (supply) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除植物"${supply.title || '未命名植物'}"的供应信息吗？此操作不可撤销。`,
      '确认删除',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    await deleteSupply(supply._id)
    ElMessage.success('供应信息删除成功')
    
    // 刷新供应列表
    await refreshSupplies()
  } catch (error) {
    if (error === 'cancel') {
      return
    }
    console.error('删除供应信息失败:', error)
    ElMessage.error('删除供应信息失败')
  }
}

// 保存供应信息编辑
const handleSaveSupplyEdit = async () => {
  try {
    // 表单验证
    if (!supplyEditFormRef.value) return
    
    const valid = await supplyEditFormRef.value.validate()
    if (!valid) return
    
    saveSupplyLoading.value = true
    
    // 准备更新数据
    const updateData = { ...supplyEditForm.value }
    delete updateData._id // 移除ID，避免更新时包含
    
    await updateSupply(supplyEditForm.value._id, updateData)
    ElMessage.success('供应信息更新成功')
    
    // 关闭编辑弹窗并刷新列表
    supplyEditDialogVisible.value = false
    await refreshSupplies()
  } catch (error) {
    console.error('保存供应信息失败:', error)
    ElMessage.error('保存供应信息失败')
  } finally {
    saveSupplyLoading.value = false
  }
}

// 删除旧格式图片 - 带确认提示
const confirmRemoveOldImage = async (index) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这张图片吗？删除后无法恢复。',
      '删除图片确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger'
      }
    )
    removeOldImage(index)
    ElMessage.success('图片删除成功')
  } catch {
    // 用户取消删除
  }
}

// 删除新格式图片 - 带确认提示
const confirmRemoveNewImage = async (index) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这张图片吗？删除后无法恢复。',
      '删除图片确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger'
      }
    )
    removeNewImage(index)
    ElMessage.success('图片删除成功')
  } catch {
    // 用户取消删除
  }
}

// 删除旧格式图片
const removeOldImage = (index) => {
  supplyEditForm.value.imageList.splice(index, 1)
}

// 删除新格式图片
const removeNewImage = (index) => {
  supplyEditForm.value.newImageList.splice(index, 1)
}

// 内联编辑标题相关方法
// 设置输入框引用
const setTitleInputRef = (el) => {
  if (el) {
    nextTick(() => {
      el.focus()
      el.select()
    })
  }
}

// 开始编辑标题
const startEditTitle = (supply) => {
  isEditingTitle.value[supply._id] = true
  editingTitleValue.value = supply.title || ''
}

// 确认编辑标题
const confirmEditTitle = async (supply) => {
  try {
    if (editingTitleValue.value.trim() === '') {
      ElMessage.warning('标题不能为空')
      return
    }
    
    if (editingTitleValue.value === supply.title) {
      // 没有变化，直接取消编辑
      cancelEditTitle(supply)
      return
    }
    
    // 调用API更新标题
    await updateSupply(supply._id, { title: editingTitleValue.value.trim() })
    
    // 更新本地数据
    const index = supplies.value.findIndex(s => s._id === supply._id)
    if (index !== -1) {
      supplies.value[index].title = editingTitleValue.value.trim()
    }
    
    // 退出编辑模式
    isEditingTitle.value[supply._id] = false
    editingTitleValue.value = ''
    
    ElMessage.success('标题更新成功')
  } catch (error) {
    console.error('更新标题失败:', error)
    ElMessage.error('更新标题失败')
  }
}

// 取消编辑标题
const cancelEditTitle = (supply) => {
  isEditingTitle.value[supply._id] = false
  editingTitleValue.value = ''
}

// 获取编辑表单的所有图片URL列表（用于预览）
const getEditFormImageUrls = () => {
  const urls = []
  
  // 添加旧格式图片
  if (supplyEditForm.value.imageList && Array.isArray(supplyEditForm.value.imageList)) {
    supplyEditForm.value.imageList.forEach(url => {
      if (url) {
        urls.push(processImageUrl(url))
      }
    })
  }
  
  // 添加新格式图片
  if (supplyEditForm.value.newImageList && Array.isArray(supplyEditForm.value.newImageList)) {
    supplyEditForm.value.newImageList.forEach(image => {
      if (image && image.url) {
        urls.push(processImageUrl(image.url))
      }
    })
  }
  
  return urls
}

// 获取总图片数量
const getTotalImageCount = () => {
  const oldCount = supplyEditForm.value.imageList ? supplyEditForm.value.imageList.length : 0
  const newCount = supplyEditForm.value.newImageList ? supplyEditForm.value.newImageList.length : 0
  return oldCount + newCount
}

// 关闭供应信息弹窗
const handleCloseSupplyDialog = () => {
  supplyDialogVisible.value = false
  currentSupplyUser.value = null
  currentUserSupplies.value = null
  supplies.value = []
}

// 关闭供应信息编辑弹窗
const handleCloseSupplyEditDialog = () => {
  supplyEditDialogVisible.value = false
  // 重置表单
  supplyEditForm.value = {
    _id: '',
    title: '',
    content: '',
    price: 0,
    price_unit: '株',
    quantity: '',
    quality: '',
    phoneNumber: '',
    address: '',
    height: null,
    meter_diameter: null,
    thorax_diameter: null,
    ground_diameter: null,
    canopy: null,
    cup: null,
    branchPos: null,
    clumpCount: null,
    clumpDiameter: null,
    imageList: [],
    newImageList: []
  }
  activeEditTab.value = 'basic'
}

// 组件挂载后初始化
onMounted(async () => {
  // 加载数据
  await loadStats()
  await loadUsers()
})
</script>

<style scoped>

.dashboard {
  min-height: 100%;
}

.page-title {
  margin-bottom: 24px;
  color: #333;
  font-size: 24px;
  font-weight: 500;
}

.stats-row {
  margin-bottom: 24px;
}

.stat-card {
  position: relative;
  overflow: hidden;
}

.stat-content {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  color: white;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  line-height: 1;
}

.stat-title {
  font-size: 14px;
  color: #666;
  margin-top: 4px;
}

/* 删除stat-trend相关样式 */

.charts-row {
  margin-bottom: 24px;
}

  .chart-card {
    height: calc(100vh - 160px);
    min-height: 600px;
    max-height: 800px;
    display: flex;
    flex-direction: column;
  }

.chart-card .large {
  height: calc(100vh - 250px);
  min-height: 450px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  align-items: center;
}

.pagination-wrapper {
  flex-shrink: 0;
  padding: 16px 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  background: #fafafa;
  border-top: 1px solid #e4e7ed;
  border-radius: 0 0 8px 8px;
}

.pagination-info {
  margin-top: 8px;
}

.custom-pagination-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.custom-total {
  flex-shrink: 0;
  margin-right: 16px;
}

.info-text {
  color: #606266;
  font-size: 13px;
  font-weight: 400;
}

/* 分页组件样式优化 */
:deep(.el-pagination) {
  --el-pagination-font-size: 14px;
  --el-pagination-button-width: 36px;
  --el-pagination-button-height: 36px;
  --el-pagination-item-gap: 6px;
}

:deep(.el-pagination .btn-prev),
:deep(.el-pagination .btn-next) {
  background: #fff;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  transition: all 0.3s;
}

:deep(.el-pagination .btn-prev:hover),
:deep(.el-pagination .btn-next:hover) {
  border-color: #409eff;
  color: #409eff;
}

:deep(.el-pagination .el-pager li) {
  background: #fff;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  margin: 0 3px;
  transition: all 0.3s;
}

:deep(.el-pagination .el-pager li:hover) {
  border-color: #409eff;
  color: #409eff;
}

:deep(.el-pagination .el-pager li.is-active) {
  background: #409eff;
  border-color: #409eff;
  color: #fff;
}

/* 确保表格卡片内容使用flex布局 */
:deep(.el-card__body) {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 20px;
}

/* 表格容器样式优化 */
.table-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  overflow: hidden;
}

/* 确保表格不会遮盖分页 */
:deep(.el-table) {
  flex: 1;
  overflow: auto;
}

:deep(.el-table__body-wrapper) {
  max-height: calc(100% - 40px);
}

.chart-container {
  height: 300px;
  width: 100%;
}

.chart-container.large {
  height: 280px;
}

/* 深色模式 */
:global(.dark) .page-title {
  color: #fff;
}

:global(.dark) .stat-value {
  color: #fff;
}

:global(.dark) .stat-title {
  color: #ccc;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dashboard {
    padding: 0;
  }
  
  .page-title {
    font-size: 20px;
    margin-bottom: 16px;
  }
  
  .stats-row {
    margin-bottom: 16px;
  }
  
  .stat-content {
    flex-direction: column;
    text-align: center;
    padding: 16px;
  }
  
  .stat-icon {
    margin-right: 0;
    margin-bottom: 8px;
    width: 40px;
    height: 40px;
  }
  
  .stat-value {
    font-size: 20px;
  }
  
  .stat-title {
    font-size: 13px;
  }
  
  .chart-card {
    margin-bottom: 16px;
    height: calc(100vh - 120px);
    min-height: 450px;
    max-height: 600px;
  }
  
  .card-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .header-actions {
    flex-direction: column;
    gap: 10px;
    align-items: stretch;
  }
  
  .header-actions .el-input {
    width: 100% !important;
    margin-right: 0 !important;
  }
  
  .header-actions .el-button {
    width: 100%;
  }
  
  /* 表格响应式 */
  :deep(.el-table) {
    font-size: 12px;
  }
  
  :deep(.el-table th),
  :deep(.el-table td) {
    padding: 8px 4px;
  }
  
  :deep(.el-table .cell) {
    padding: 0 4px;
  }
  
  /* 分页响应式调整 */
  .pagination-wrapper {
    padding: 12px 8px;
    flex-direction: column;
    gap: 8px;
  }
  
  :deep(.el-pagination) {
    --el-pagination-font-size: 12px;
    --el-pagination-button-width: 28px;
    --el-pagination-button-height: 28px;
    --el-pagination-item-gap: 4px;
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .info-text {
    font-size: 11px;
    text-align: center;
  }
  
  /* 移动端分页布局调整 */
  :deep(.el-pagination .el-pagination__sizes) {
    order: -1;
    margin-bottom: 8px;
  }
  
  :deep(.el-pagination .el-pagination__total) {
    order: -2;
    margin-bottom: 4px;
    text-align: center;
  }
  
  :deep(.el-pagination .el-pagination__jump) {
    margin-left: 0;
    margin-top: 8px;
  }
}

/* 平板设备适配 */
@media (min-width: 769px) and (max-width: 1024px) {
  .stat-content {
    padding: 18px;
  }
  
  .stat-value {
    font-size: 22px;
  }
  
  .chart-card {
    height: calc(100vh - 140px);
    min-height: 550px;
  }
  
  :deep(.el-table) {
    font-size: 13px;
  }
}

/* 超小屏幕优化 */
@media (max-width: 480px) {
  .page-title {
    font-size: 18px;
    margin-bottom: 12px;
  }
  
  .stats-row {
    margin-bottom: 12px;
  }
  
  .stat-content {
    padding: 12px;
  }
  
  .stat-icon {
    width: 36px;
    height: 36px;
  }
  
  .stat-value {
    font-size: 18px;
  }
  
  .stat-title {
    font-size: 12px;
  }
  
  .chart-card {
    height: calc(100vh - 100px);
    min-height: 400px;
  }
  
  :deep(.el-card__header) {
    padding: 12px;
  }
  
  :deep(.el-card__body) {
    padding: 12px;
  }
  
  .header-actions .el-button {
    padding: 8px 12px;
    font-size: 12px;
  }
  
  .pagination-wrapper {
    padding: 8px 4px;
  }
  
  :deep(.el-pagination) {
    --el-pagination-button-width: 24px;
    --el-pagination-button-height: 24px;
    --el-pagination-font-size: 11px;
  }
  
  .info-text {
    font-size: 10px;
  }
}

/* 用户管理相关样式 */
.user-detail {
  margin: 20px 0;
}

.text-gray {
  color: #999;
}

:deep(.el-descriptions__label) {
  font-weight: 600;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

.el-button + .el-button {
  margin-left: 8px;
}

/* 编辑供应列样式 */
.supply-edit-btn {
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.supply-edit-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
}

.no-supply-tag {
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 4px;
  background-color: #f5f7fa;
  color: #909399;
  border: 1px solid #e4e7ed;
}

/* 用户ID列折叠功能样式 */
.user-id-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.3s ease;
  user-select: none;
}

.user-id-header:hover {
  background-color: #f5f7fa;
}

.collapse-icon {
  font-size: 12px;
  color: #909399;
  transition: transform 0.3s ease;
}

.collapse-icon.expanded {
  transform: rotate(90deg);
}

.user-id-short {
  font-size: 12px;
  color: #606266;
  cursor: pointer;
}

.user-id-full {
  font-size: 13px;
  color: #303133;
  word-break: break-all;
  line-height: 1.4;
}

/* 深色模式适配 */
:global(.dark) .user-id-header:hover {
  background-color: #262626;
}

:global(.dark) .user-id-short {
  color: #ccc;
}

:global(.dark) .user-id-full {
  color: #fff;
}

/* 位置信息相关样式 */
.location-detail {
  margin: 20px 0;
}

.address-components {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.poi-list {
  max-height: 200px;
  overflow-y: auto;
}

.poi-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 0;
  border-bottom: 1px solid #f0f0f0;
}

.poi-item:last-child {
  border-bottom: none;
}

.poi-distance {
  color: #999;
  margin-left: auto;
}

/* 供应信息相关样式 */
.supply-dialog :deep(.el-dialog__body) {
  padding: 10px 20px;
  max-height: 80vh;
  overflow-y: auto;
}

/* 供应信息编辑弹窗样式优化 */
.supply-edit-dialog {
  border-radius: 12px;
  overflow: hidden;
}

.supply-edit-dialog :deep(.el-dialog) {
  margin: 5vh auto !important;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.supply-edit-dialog :deep(.el-dialog__header) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 16px 20px;
  margin: 0;
  flex-shrink: 0;
}

.supply-edit-dialog :deep(.el-dialog__title) {
  color: white;
  font-size: 16px;
  font-weight: 600;
}

.supply-edit-dialog :deep(.el-dialog__headerbtn .el-dialog__close) {
  color: white;
  font-size: 20px;
}

.supply-edit-dialog :deep(.el-dialog__body) {
  padding: 0;
  background-color: #f8fafc;
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.supply-edit-dialog :deep(.el-dialog__footer) {
  flex-shrink: 0;
}

.supply-edit-form {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.supply-edit-tabs {
  height: 100%;
  background: transparent;
  display: flex;
  flex-direction: column;
}

.supply-edit-tabs :deep(.el-tabs__header) {
  margin: 0;
  background: white;
  padding: 0 20px;
  border-bottom: 1px solid #e5e7eb;
  flex-shrink: 0;
}

.supply-edit-tabs :deep(.el-tabs__nav-wrap) {
  padding: 12px 0;
}

.supply-edit-tabs :deep(.el-tabs__item) {
  font-size: 14px;
  font-weight: 500;
  padding: 8px 20px;
  border-radius: 6px;
  margin-right: 6px;
  transition: all 0.3s ease;
}

.supply-edit-tabs :deep(.el-tabs__item.is-active) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
}

.supply-edit-tabs :deep(.el-tabs__content) {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.supply-edit-tabs :deep(.el-tab-pane) {
  height: 100%;
  overflow-y: auto;
  padding: 16px;
}

/* 基本信息容器样式 */
.basic-info-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.info-section {
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
}

.info-section:hover {
  border-color: #667eea;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.info-section :deep(.el-card__header) {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-bottom: 1px solid #e5e7eb;
  padding: 12px 16px;
}

.info-section :deep(.el-card__body) {
  padding: 16px;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.section-icon {
  font-size: 16px;
  color: #667eea;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

/* 表单项样式优化 */
.supply-edit-form :deep(.el-form-item) {
  margin-bottom: 12px;
  display: flex;
  align-items: flex-start;
}

.supply-edit-form :deep(.el-form-item__label) {
  font-weight: 500;
  color: #374151;
  font-size: 13px;
  line-height: 1.4;
  width: 100px !important;
  text-align: right;
  padding-right: 12px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  min-height: 32px;
}

.supply-edit-form :deep(.el-form-item__content) {
  flex: 1;
  display: flex;
  align-items: center;
  min-height: 32px;
}

/* 文本域表单项特殊处理 */
.supply-edit-form :deep(.el-form-item:has(.el-textarea)) {
  align-items: flex-start;
}

.supply-edit-form :deep(.el-form-item:has(.el-textarea) .el-form-item__label) {
  align-items: flex-start;
  padding-top: 8px;
}

.supply-edit-form :deep(.el-form-item:has(.el-textarea) .el-form-item__content) {
  align-items: flex-start;
}

.supply-edit-form :deep(.el-input__wrapper) {
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  width: 100%;
}

.supply-edit-form :deep(.el-input__wrapper:hover) {
  box-shadow: 0 2px 6px rgba(102, 126, 234, 0.2);
}

.supply-edit-form :deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.supply-edit-form :deep(.el-textarea__inner) {
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  width: 100%;
}

.supply-edit-form :deep(.el-textarea__inner:hover) {
  box-shadow: 0 2px 6px rgba(102, 126, 234, 0.2);
}

.supply-edit-form :deep(.el-textarea__inner:focus) {
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

/* 确保选择器和数字输入框也正确对齐 */
.supply-edit-form :deep(.el-select) {
  width: 100%;
}

.supply-edit-form :deep(.el-input-number) {
  width: 100%;
}

.supply-edit-form :deep(.el-input-number .el-input__wrapper) {
  width: 100%;
}

/* 图片管理容器样式 */
.image-management-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.image-section {
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.image-section :deep(.el-card__header) {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-bottom: 1px solid #e5e7eb;
  padding: 12px 16px;
  flex-shrink: 0;
}

.image-section :deep(.el-card__body) {
  padding: 16px;
  flex: 1;
  overflow-y: auto;
}

.image-stats {
  margin-left: auto;
}

.image-list-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e5e7eb;
}

.image-list-header h4 {
  margin: 0;
  color: #374151;
  font-size: 14px;
  font-weight: 600;
}

/* 弹窗底部样式 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 20px;
  background: white;
  border-top: 1px solid #e5e7eb;
}

.dialog-footer .el-button {
  padding: 8px 20px;
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.dialog-footer .el-button--primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
}

.dialog-footer .el-button--primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

/* 供应信息头部样式 */
.supply-header {
  margin-bottom: 24px;
  padding: 20px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 12px;
  border: 1px solid #e5e7eb;
}

.supply-header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.user-avatar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-weight: 600;
  font-size: 16px;
  border: 2px solid white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.user-avatar :deep(.el-avatar__inner) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-weight: 600;
  font-size: 16px;
}

.user-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.user-name {
  margin: 0;
  color: #1f2937;
  font-size: 18px;
  font-weight: 600;
}

.supply-count {
  color: #6b7280;
  font-size: 13px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 供应信息列表样式 */
.supply-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.supply-item {
  margin-bottom: 0;
}

.supply-card {
  border-radius: 12px;
  transition: all 0.3s ease;
  border: 1px solid #e5e7eb;
  overflow: hidden;
}

.supply-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
  border-color: #667eea;
}

.supply-card :deep(.el-card__header) {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-bottom: 1px solid #e5e7eb;
  padding: 16px 20px;
}

.supply-card :deep(.el-card__body) {
  padding: 20px;
}

.supply-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.supply-title-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.supply-status-tag {
  font-weight: 500;
}

.supply-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.supply-actions {
  display: flex;
  gap: 8px;
}

/* 供应内容区域样式 */
.supply-content {
  margin-top: 0;
}

.supply-info-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.price-quantity-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 12px;
}

.price-info {
  flex: 1;
}

.price-tag {
  font-size: 16px;
  font-weight: 600;
  padding: 8px 16px;
  border-radius: 8px;
}

.quantity-info {
  display: flex;
  gap: 8px;
  align-items: center;
}

.description-section {
  padding: 12px 16px;
  background: #f8fafc;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.supply-description {
  margin: 0;
  color: #4b5563;
  line-height: 1.6;
  font-size: 14px;
}

.contact-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 0;
}

.contact-icon {
  color: #667eea;
  font-size: 14px;
}

.contact-text {
  color: #4b5563;
  font-size: 13px;
}

/* 规格参数样式 */
.supply-specs {
  background: #f9fafb;
  border-radius: 8px;
  padding: 16px;
  height: 100%;
}

.specs-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e5e7eb;
}

.specs-icon {
  color: #667eea;
  font-size: 16px;
}

.specs-title {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.specs-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.spec-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 8px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
  transition: all 0.2s ease;
}

.spec-item:hover {
  border-color: #667eea;
  background: #f0f4ff;
}

.spec-label {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
}

.spec-value {
  font-size: 13px;
  color: #1f2937;
  font-weight: 600;
}

/* 图片区域样式 */
.supply-images {
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
}

.images-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.images-icon {
  color: #667eea;
  font-size: 16px;
}

.images-title {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.image-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  gap: 12px;
}

.image-item {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.image-item:hover {
  transform: scale(1.05);
  border-color: #667eea;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.supply-image {
  width: 100%;
  height: 100px;
  border-radius: 6px;
}

.image-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100px;
  background-color: #f5f5f5;
  color: #999;
  font-size: 12px;
}

.image-time {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  color: white;
  font-size: 10px;
  padding: 4px 6px;
  text-align: center;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.image-item:hover .image-overlay {
  opacity: 1;
}

.preview-icon {
  color: white;
  font-size: 20px;
}

.image-actions {
  position: absolute;
  top: 4px;
  right: 4px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.image-item:hover .image-actions {
  opacity: 1;
}

.empty-supply {
  text-align: center;
  padding: 40px 20px;
}

.loading-container {
  padding: 20px;
}

.image-management {
  padding: 0;
}

.image-list {
  margin-bottom: 24px;
}

.image-list h4 {
  margin: 0 0 12px 0;
  color: #333;
  font-size: 14px;
  font-weight: 600;
}

.no-images {
  text-align: center;
  padding: 40px 20px;
}

/* 响应式设计 - 供应信息 */
@media (max-width: 768px) {
  .supply-dialog :deep(.el-dialog) {
    width: 95% !important;
    margin: 0 auto;
  }

  .supply-header-content {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .user-info {
    justify-content: center;
  }

  .user-name {
    font-size: 16px;
  }

  .header-actions {
    justify-content: center;
  }

  .supply-content .el-row {
    flex-direction: column;
  }

  .supply-content .el-col {
    width: 100% !important;
  }

  .price-quantity-row {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .specs-grid {
    grid-template-columns: 1fr;
  }

  .image-grid {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 8px;
  }

  .supply-image {
    height: 100px;
  }

  .image-error {
    height: 100px;
  }

  .supply-card-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .supply-title-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .supply-actions {
    width: 100%;
    justify-content: space-between;
  }

  .user-avatar {
    width: 32px;
    height: 32px;
    font-size: 14px;
  }

  .user-name {
    font-size: 14px;
  }

  .supply-count {
    font-size: 12px;
  }

  .price-tag {
    font-size: 14px;
    padding: 6px 12px;
  }

  .contact-section {
    gap: 6px;
  }

  .contact-item {
    padding: 4px 0;
  }

  .contact-text {
    font-size: 12px;
  }

  /* 供应信息编辑弹窗响应式 */
  .supply-edit-dialog :deep(.el-dialog) {
    width: 95% !important;
    margin: 2vh auto !important;
    max-height: 96vh;
  }

  .supply-edit-tabs :deep(.el-tab-pane) {
    padding: 16px;
  }

  .basic-info-container {
    gap: 16px;
  }

  .info-section :deep(.el-card__body) {
    padding: 16px;
  }

  .section-header {
    gap: 8px;
  }

  .section-icon {
    font-size: 18px;
  }

  .section-title {
    font-size: 14px;
  }

  .supply-edit-form :deep(.el-form-item) {
    margin-bottom: 16px;
    flex-direction: column;
    align-items: stretch;
  }

  .supply-edit-form :deep(.el-form-item__label) {
    font-size: 13px;
    width: auto !important;
    text-align: left;
    padding-right: 0;
    padding-bottom: 4px;
    justify-content: flex-start;
    min-height: auto;
  }

  .supply-edit-form :deep(.el-form-item__content) {
    min-height: auto;
  }

  .dialog-footer {
    padding: 16px;
    gap: 8px;
  }

  .dialog-footer .el-button {
    padding: 10px 20px;
    font-size: 14px;
  }

  .image-section :deep(.el-card__body) {
    padding: 16px;
  }

  .image-list-header h4 {
    font-size: 14px;
  }
}

/* 平板设备适配 - 供应信息编辑 */
@media (min-width: 769px) and (max-width: 1024px) {
  .supply-edit-dialog :deep(.el-dialog) {
    width: 90% !important;
  }

  .supply-edit-tabs :deep(.el-tabs__content) {
    padding: 20px;
  }

  .info-section :deep(.el-card__body) {
    padding: 20px;
  }
}

/* 超小屏幕优化 - 供应信息编辑 */
@media (max-width: 480px) {
  .supply-edit-dialog :deep(.el-dialog) {
    width: 100% !important;
    max-height: 100vh;
    margin: 0 !important;
    border-radius: 0;
  }

  .supply-edit-tabs :deep(.el-tabs__header) {
    padding: 0 12px;
  }

  .supply-edit-tabs :deep(.el-tabs__nav-wrap) {
    padding: 12px 0;
  }

  .supply-edit-tabs :deep(.el-tabs__item) {
    font-size: 14px;
    padding: 8px 16px;
    margin-right: 4px;
  }

  .supply-edit-tabs :deep(.el-tab-pane) {
    padding: 12px;
  }

  .basic-info-container {
    gap: 12px;
  }

  .info-section :deep(.el-card__header) {
    padding: 12px 16px;
  }

  .info-section :deep(.el-card__body) {
    padding: 12px;
  }

  .section-header {
    gap: 6px;
  }

  .section-icon {
    font-size: 16px;
  }

  .section-title {
    font-size: 13px;
  }

  .supply-edit-form :deep(.el-form-item) {
    margin-bottom: 12px;
    flex-direction: column;
    align-items: stretch;
  }

  .supply-edit-form :deep(.el-form-item__label) {
    font-size: 12px;
    line-height: 1.4;
    width: auto !important;
    text-align: left;
    padding-right: 0;
    padding-bottom: 4px;
    justify-content: flex-start;
    min-height: auto;
  }

  .supply-edit-form :deep(.el-form-item__content) {
    min-height: auto;
  }

  .dialog-footer {
    padding: 12px;
    gap: 6px;
  }

  .dialog-footer .el-button {
    padding: 8px 16px;
    font-size: 13px;
  }

  .image-section :deep(.el-card__header) {
    padding: 12px 16px;
  }

  .image-section :deep(.el-card__body) {
    padding: 12px;
  }

  .image-list-header {
    margin-bottom: 12px;
    padding-bottom: 8px;
  }

  .image-list-header h4 {
    font-size: 13px;
  }

  .image-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 6px;
  }

  .supply-image {
    height: 80px;
  }
}

/* 内联编辑标题样式 */
.supply-title-container {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.supply-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  cursor: pointer;
  transition: color 0.2s;
  flex: 1;
}

.supply-title:hover {
  color: #409eff;
}

.supply-title-edit {
  flex: 1;
  max-width: 300px;
}

.supply-title-edit .el-input {
  --el-input-height: 32px;
}

.title-edit-btn {
  padding: 4px 6px !important;
  min-height: auto !important;
  opacity: 0;
  transition: opacity 0.2s;
}

.supply-title-container:hover .title-edit-btn {
  opacity: 1;
}

.title-edit-btn:hover {
  color: #409eff !important;
  background-color: #ecf5ff !important;
}

.supply-title-section {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.supply-status-tag {
  white-space: nowrap;
  flex-shrink: 0;
}

/* 图片加载和错误状态样式 */
.image-loading,
.image-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #909399;
  font-size: 12px;
  gap: 4px;
}

.image-loading .el-icon {
  font-size: 20px;
  animation: rotate 2s linear infinite;
}

.image-error .el-icon {
  font-size: 20px;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 图片预览增强 */
.supply-image {
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
}

.supply-image:hover {
  transform: scale(1.02);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 企业级图片预览弹窗样式 */
.image-preview-dialog :deep(.el-overlay) {
  background: rgba(23, 25, 35, 0.95) !important;
  backdrop-filter: blur(8px) !important;
  -webkit-backdrop-filter: blur(8px) !important;
}

.image-preview-dialog :deep(.el-overlay-dialog) {
  background: transparent !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.image-preview-dialog :deep(.el-modal-dialog) {
  background: #ffffff !important;
  border-radius: 16px !important;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.4), 
              0 0 0 1px rgba(255, 255, 255, 0.05) !important;
  overflow: hidden !important;
  max-width: 90vw !important;
  max-height: 90vh !important;
  margin: 0 !important;
  border: none !important;
}

.image-preview-dialog :deep(.el-dialog) {
  background: transparent !important;
  box-shadow: none !important;
  border-radius: 0 !important;
  margin: 0 !important;
  width: 100% !important;
  height: 100% !important;
}

.image-preview-dialog :deep(.el-dialog__header) {
  display: none !important;
}

.image-preview-dialog :deep(.el-dialog__body) {
  padding: 0 !important;
  background: transparent !important;
  height: 100% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.image-preview-dialog :deep(.el-dialog__footer) {
  display: none !important;
}

.image-preview-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.image-preview-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  padding: 24px;
  cursor: default;
  background: transparent;
}

.main-image-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  position: relative;
  overflow: hidden;
}

.preview-main-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  cursor: zoom-out;
  transition: transform 0.3s ease;
  border-radius: 8px;
}

.close-btn {
  position: absolute;
  top: 16px;
  right: 16px;
  width: 36px;
  height: 36px;
  background: rgba(15, 23, 42, 0.8);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #ffffff;
  transition: all 0.2s ease;
  z-index: 10;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
}

.close-btn:hover {
  background: rgba(15, 23, 42, 0.95);
  border-color: rgba(255, 255, 255, 0.2);
  transform: scale(1.05);
}







/* image-overlay 样式 */
.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  cursor: pointer;
  z-index: 2;
}

.image-item:hover .image-overlay {
  opacity: 1;
}

.preview-icon {
  color: white;
  font-size: 24px;
  transition: transform 0.2s ease;
}

.image-overlay:hover .preview-icon {
  transform: scale(1.2);
}
</style>